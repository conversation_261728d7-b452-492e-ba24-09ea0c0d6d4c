Code.require_file("repos.ex", __DIR__)

Code.require_file("ecto/test_schemas.ex", __DIR__)
Code.require_file("ecto/user_group_schemas.ex", __DIR__)

if Mix.env() == :test do
  # Keep the original TestRepo as a simple SQLite repo for Operations tests
  defmodule Drops.TestRepo do
    use Ecto.Repo,
      otp_app: :drops,
      adapter: Ecto.Adapters.SQLite3
  end
end

Application.ensure_all_started(:drops)
